import 'dart:async';
import 'dart:io';

import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/connectivity_plus/bloc/connectivity_bloc.dart';
import 'package:flowkar/features/discover/bloc/discover_bloc.dart';
import 'package:flowkar/features/discover/page/discover_post_detail_widget.dart';
import 'package:flowkar/features/discover/page/hashtag/hashtag_post_widget.dart';
import 'package:flowkar/features/discover/widget/discover_page_shimmer.dart';
import 'package:flowkar/features/reels_screen/presentation/pages/video_page.dart';
import 'package:flowkar/features/reels_screen/service/reel_service.dart';
import 'package:flowkar/features/widgets/common/get_user_profile_by_id/get_user_profile_by_id.dart';
import 'package:flowkar/features/widgets/custom/all_caught_up_massage.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:liquid_pull_to_refresh/liquid_pull_to_refresh.dart';

class DiscoverPage extends StatefulWidget {
  const DiscoverPage({super.key});

  static Widget builder(BuildContext context) {
    return DiscoverPage();
  }

  @override
  State<DiscoverPage> createState() => _DiscoverPageState();
}

class _DiscoverPageState extends State<DiscoverPage> with SingleTickerProviderStateMixin {
  late ConnectivityState? connectivityState;
  late ScrollController discoverscrollController;
  final TextEditingController _searchController = TextEditingController();
  late TabController _tabController;
  bool showCloseButton = false;
  bool _showAllCaughtUp = false;
  Timer? _allCaughtUpTimer;
  bool _hasShownAllCaughtUp = false;
  bool _isAtBottom = false;
  bool _wasAtBottomBefore = false;

  Timer? _searchDebounceTimer;
  bool _isSearching = false;

  bool _isRefreshing = false;

  // Offline message state
  Timer? _offlineMessageTimer;
  bool _showOfflineMessage = false;
  bool _isRefreshAttemptedOffline = false;

  @override
  void initState() {
    super.initState();
    connectivityState = context.read<ConnectivityBloc>().state;

    _searchController.clear();
    discoverscrollController = ScrollController();
    _tabController = TabController(length: 2, vsync: this);
    context.read<HomeFeedBloc>().add(DiscoverPostApiEvent(page: 1));

    if (mounted) {
      _searchController.addListener(_updateCloseButtonVisibility);
    }
  }

  Future<void> _refreshFeed() async {
    connectivityState = context.read<ConnectivityBloc>().state;

    if (!connectivityState!.isConnected) {
      _isRefreshAttemptedOffline = true;

      _offlineMessageTimer?.cancel();
      _offlineMessageTimer = Timer(const Duration(seconds: 3), () {
        if (mounted && _isRefreshAttemptedOffline) {
          setState(() {
            _showOfflineMessage = true;
          });

          Timer(const Duration(seconds: 3), () {
            if (mounted) {
              setState(() {
                _showOfflineMessage = false;
                _isRefreshAttemptedOffline = false;
              });
            }
          });
        }
      });

      return;
    } else {
      final state = context.read<HomeFeedBloc>().state;
      setState(() {
        _isRefreshing = true;
        _showAllCaughtUp = false;
        _hasShownAllCaughtUp = false;
        _isAtBottom = false;
        _wasAtBottomBefore = false;
      });

      _isRefreshAttemptedOffline = false;
      _showOfflineMessage = false;
      _allCaughtUpTimer?.cancel();

      if (state.isDiscoverposts.isNotEmpty) {
        state.isDiscoverposts.clear();
      }
      context.read<HomeFeedBloc>().add(DiscoverPostApiEvent(page: 1));
      context.read<DiscoverBloc>().add(DiscoverHashtagListEvent(searchtext: _searchController.text));
      context.read<DiscoverBloc>().add(DiscoverSearchUserListEvent(searchtext: _searchController.text));

      setState(() {
        _isRefreshing = false;
      });
    }
  }

  void _updateCloseButtonVisibility() {
    if (mounted) {
      setState(() {
        showCloseButton = _searchController.text.isNotEmpty;
        if (_searchController.text.contains('#')) {
          _tabController.animateTo(1);
        }
      });
    }
  }

  void _performSearch(String query) {
    _searchDebounceTimer?.cancel();

    if (query.isEmpty) {
      setState(() {
        _isSearching = false;
      });
      context.read<DiscoverBloc>().add(SearchQueryChanged(''));
      return;
    }

    setState(() {
      _isSearching = true;
    });

    _searchDebounceTimer = Timer(const Duration(milliseconds: 300), () {
      if (mounted) {
        context.read<DiscoverBloc>().add(SearchQueryChanged(query));
        if (query.contains('#')) {
          context.read<DiscoverBloc>().add(DiscoverHashtagListEvent(searchtext: query));
        } else {
          context.read<DiscoverBloc>().add(DiscoverHashtagListEvent(searchtext: query));
          context.read<DiscoverBloc>().add(DiscoverSearchUserListEvent(searchtext: query));
        }
      }
    });

    Future.delayed(Duration(seconds: 1), () {
      setState(() {
        _isSearching = false;
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    discoverscrollController.dispose();
    _allCaughtUpTimer?.cancel();
    _searchDebounceTimer?.cancel();
    _searchController.dispose();
    _offlineMessageTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    connectivityState = context.read<ConnectivityBloc>().state;
    return SafeArea(
      child: Stack(
        alignment: Alignment.center,
        children: [
          Scaffold(
            resizeToAvoidBottomInset: false,
            body: BlocBuilder<DiscoverBloc, DiscoverState>(
              builder: (context, state) {
                return InkWell(
                  focusColor: Colors.transparent,
                  onTap: () {
                    FocusManager.instance.primaryFocus?.unfocus();
                  },
                  child: Padding(
                    padding: EdgeInsets.only(top: 16.0.h),
                    child: Column(
                      children: [
                        _buildSearchBar(context),
                        BlocListener<ConnectivityBloc, ConnectivityState>(
                          listener: (context, state) {
                            if (state.isReconnected) {
                              if (context.read<HomeFeedBloc>().state.isDiscoverposts.isEmpty) {
                                _refreshFeed();
                              }
                            }
                          },
                          child: state.searchQuery.isEmpty ? _recentpostview() : _buildSearchResults(context, state),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
          _buildOfflineMessage(_showOfflineMessage),
        ],
      ),
    );
  }

  Widget _buildSearchBar(BuildContext context) {
    return ValueListenableBuilder<TextEditingValue>(
      valueListenable: _searchController,
      builder: (context, value, child) {
        return Container(
          padding: EdgeInsets.only(left: 16.0.w, right: 16.0.w),
          child: FlowkarTextFormField(
            hint: Lang.of(context).lbl_search,
            context: context,
            controller: _searchController,
            prefixIcon: CustomImageView(
              imagePath: AssetConstants.icSearch,
              height: 10,
              width: 10,
              margin: EdgeInsets.all(15.0),
            ),
            textStyle: Theme.of(context)
                .textTheme
                .bodyMedium
                ?.copyWith(fontSize: 16.sp, color: Theme.of(context).customColors.black),
            hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontSize: 16.sp, color: Theme.of(context).customColors.greylite, fontWeight: FontWeight.w500),
            onChanged: (value) {
              _performSearch(value);
            },
            fillColor: ThemeData().customColors.fillcolor,
            borderDecoration: OutlineInputBorder(
              borderRadius: BorderRadius.all(Radius.circular(10.r)),
              borderSide: BorderSide.none,
            ),
            filled: true,
            contentPadding: EdgeInsets.symmetric(horizontal: 16.w),
            suffixIcon: value.text.isNotEmpty
                ? CustomImageView(
                    imagePath: AssetConstants.icClose,
                    height: 10.0.h,
                    width: 10.0.w,
                    margin: EdgeInsets.all(16.5),
                    onTap: () {
                      FocusManager.instance.primaryFocus?.unfocus();
                      _searchController.clear();
                      _searchDebounceTimer?.cancel();
                      setState(() {
                        _isSearching = false;
                      });
                      context.read<DiscoverBloc>().add(SearchQueryChanged(''));
                    },
                  )
                : null,
          ),
        );
      },
    );
  }

  Widget _recentpostview() {
    return Expanded(
      child: LiquidPullToRefresh(
        color: Theme.of(context).primaryColor.withOpacity(0.5),
        showChildOpacityTransition: false,
        backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.5),
        onRefresh: _refreshFeed,
        child: Padding(
          padding: EdgeInsets.only(top: 16.0.h, left: 16.0.w, right: 16.0.w),
          child: BlocBuilder<DiscoverBloc, DiscoverState>(
            builder: (context, state) {
              return BlocBuilder<HomeFeedBloc, HomeFeedState>(
                builder: (context, homeFeedState) {
                  return NotificationListener<ScrollNotification>(
                    onNotification: (ScrollNotification scrollInfo) {
                      bool currentlyAtBottom = scrollInfo.metrics.pixels >= scrollInfo.metrics.maxScrollExtent - 50;

                      if (scrollInfo is ScrollEndNotification) {
                        final nextPage = homeFeedState.getAllPostModel?.nextPage;
                        final isLoading = homeFeedState.isDiscoverLoadingMore;
                        final hasMoreData = nextPage != null;
                        final isPaginationComplete = !hasMoreData && !isLoading;

                        final isShimmerLoading = homeFeedState.isdiscoverloding;

                        if (currentlyAtBottom && hasMoreData && !isLoading) {
                          context.read<HomeFeedBloc>().add(
                                DiscoverPostApiEvent(page: homeFeedState.isDiscoverpage + 1),
                              );
                        } else if (currentlyAtBottom && isPaginationComplete && !isShimmerLoading) {
                          if (!_wasAtBottomBefore || !_hasShownAllCaughtUp) {
                            setState(() {
                              _showAllCaughtUp = true;
                              _hasShownAllCaughtUp = true;
                            });

                            _allCaughtUpTimer?.cancel();
                            _allCaughtUpTimer = Timer(const Duration(seconds: 3), () {
                              if (mounted) {
                                setState(() {
                                  _showAllCaughtUp = false;
                                });
                              }
                            });
                          }
                        }

                        _wasAtBottomBefore = currentlyAtBottom;
                        _isAtBottom = currentlyAtBottom;

                        return true;
                      }

                      if (scrollInfo is ScrollUpdateNotification) {
                        bool newAtBottom = scrollInfo.metrics.pixels >= scrollInfo.metrics.maxScrollExtent - 50;
                        if (_isAtBottom != newAtBottom) {
                          _isAtBottom = newAtBottom;
                          if (!newAtBottom) {
                            _wasAtBottomBefore = false;
                          }
                        }
                      }

                      return false;
                    },
                    child: Stack(
                      children: [
                        BlocBuilder<ConnectivityBloc, ConnectivityState>(
                          builder: (context, connectivityState) {
                            if (!connectivityState.isConnected && (homeFeedState.isdiscoverloding || _isRefreshing)) {
                              return DiscoverPageShimmer();
                            } else if (homeFeedState.isdiscoverloding) {
                              return DiscoverPageShimmer();
                            } else if (!connectivityState.isConnected && homeFeedState.isDiscoverposts.isEmpty) {
                              return DiscoverPageShimmer();
                            } else if (homeFeedState.isDiscoverposts.isEmpty) {
                              if (!connectivityState.isConnected && (homeFeedState.isdiscoverloding || _isRefreshing)) {
                                return ListView(
                                  physics: AlwaysScrollableScrollPhysics(),
                                  children: [
                                    buildSizedBoxH(MediaQuery.of(context).size.height / 6),
                                    ExceptionWidget(
                                      imagePath: Assets.images.svg.exception.svgNodatafound.path,
                                      showButton: false,
                                      title: Lang.of(context).lbl_no_data_found,
                                      subtitle: Lang.of(context).lbl_no_post,
                                    ),
                                  ],
                                );
                              } else {
                                return DiscoverPageShimmer();
                              }
                            }

                            return StaggeredGridView.countBuilder(
                              padding: EdgeInsets.only(bottom: Platform.isAndroid ? 70.h : 44.h),
                              physics: Platform.isIOS ? AlwaysScrollableScrollPhysics() : BouncingScrollPhysics(),
                              shrinkWrap: true,
                              crossAxisCount: 2,
                              mainAxisSpacing: 2.0,
                              crossAxisSpacing: 2.0,
                              staggeredTileBuilder: (int index) {
                                return StaggeredTile.count(1, index.isEven ? 1.3 : 1);
                              },
                              itemCount: homeFeedState.isDiscoverposts.length,
                              itemBuilder: (context, index) {
                                final filePath = homeFeedState.isDiscoverposts[index].files.isNotEmpty
                                    ? homeFeedState.isDiscoverposts[index].files.first
                                    : Assets.images.pngs.other.pngPlaceholder.path;

                                return Container(
                                  clipBehavior: Clip.antiAlias,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(18.0.r),
                                      border:
                                          Border.all(color: Theme.of(context).primaryColor.withOpacity(0.1), width: 2)),
                                  child: isVideo(filePath)
                                      ? homeFeedState.isDiscoverposts[index].thumbnailFiles!.isNotEmpty
                                          ? GestureDetector(
                                              onTap: () {
                                                Logger.lOG(
                                                    "Click ${homeFeedState.isDiscoverposts[index].thumbnailFiles!},    $index , ${homeFeedState.isDiscoverposts[index].id}");
                                                FocusManager.instance.primaryFocus?.unfocus();
                                                PersistentNavBarNavigator.pushNewScreen(context,
                                                    screen: VideoReelPage(
                                                      index: 0,
                                                      reelService: ReelService(),
                                                      screen: 'Discover',
                                                      postdata: homeFeedState.isDiscoverposts[index],
                                                    ),
                                                    withNavBar: false);
                                                Logger.lOG("widget.postdata Before ${homeFeedState.posts[index].id}");
                                                // FocusManager.instance.primaryFocus?.unfocus();
                                                // PersistentNavBarNavigator.pushNewScreen(context,
                                                //     screen: DiscoverUserPostListScreen(
                                                //         initialIndex: index,
                                                //         discoverfpost: homeFeedState.isDiscoverposts),
                                                //     withNavBar: false);
                                              },
                                              child: Stack(
                                                fit: StackFit.expand,
                                                children: [
                                                  CustomImageView(
                                                    width: double.infinity,
                                                    imagePath:
                                                        homeFeedState.isDiscoverposts[index].thumbnailFiles?.first,
                                                    fit: BoxFit.cover,
                                                    radius: BorderRadius.circular(16.0.r),
                                                  ),
                                                  Column(
                                                    crossAxisAlignment: CrossAxisAlignment.start,
                                                    children: [
                                                      CustomImageView(
                                                        margin: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
                                                        imagePath: Assets.images.svg.other.svgPlayIconWhite.path,
                                                        color: Colors.white70,
                                                        height: 30.h,
                                                        width: 30.w,
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            )
                                          : Container(
                                              decoration: BoxDecoration(
                                                color: Colors.grey.shade300,
                                                image: DecorationImage(
                                                  image: AssetImage(Assets.images.pngs.other.pngPlaceholder.path),
                                                ),
                                                borderRadius: BorderRadius.circular(16.0.r),
                                              ),
                                              child: Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  Padding(
                                                    padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
                                                    child: CustomImageView(
                                                        height: 30.h,
                                                        width: 30.w,
                                                        imagePath: Assets.images.svg.other.icPlayVideo.path,
                                                        radius: BorderRadius.circular(16.0.r)),
                                                  ),
                                                ],
                                              ),
                                            )
                                      : GestureDetector(
                                          onTap: () {
                                            FocusManager.instance.primaryFocus?.unfocus();
                                            PersistentNavBarNavigator.pushNewScreen(context,
                                                screen: DiscoverUserPostListScreen(
                                                    initialIndex: index, discoverfpost: homeFeedState.isDiscoverposts),
                                                withNavBar: false);
                                          },
                                          child: CustomImageView(
                                            imagePath: filePath,
                                            fit: BoxFit.cover,
                                            radius: BorderRadius.circular(16.0.r),
                                          ),
                                        ),
                                );
                              },
                            );
                          },
                        ),
                        AllCaughtUpMassage(showAllCaughtUp: _showAllCaughtUp && !homeFeedState.isdiscoverloding),
                        Padding(
                          padding: EdgeInsets.only(bottom: Platform.isAndroid ? 50.h : 60.h),
                          child: Align(
                            alignment: Alignment.bottomCenter,
                            child: Visibility(
                              visible: homeFeedState.isDiscoverLoadingMore &&
                                  !homeFeedState.isdiscoverloding &&
                                  (connectivityState?.isConnected ?? false),
                              child: SizedBox(
                                height: 50.h,
                                child: Center(
                                    child: CupertinoActivityIndicator(color: Theme.of(context).colorScheme.primary)),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildSearchResults(BuildContext context, DiscoverState state) {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.only(left: 16.0.w, right: 16.0.w),
        child: Column(
          children: [
            buildSizedBoxH(18),
            _buildTabBar(),
            buildSizedBoxH(13),
            Expanded(child: _buildTabContent(state)),
          ],
        ),
      ),
    );
  }

  Widget _buildTabContent(DiscoverState state) {
    return BlocBuilder<DiscoverBloc, DiscoverState>(builder: (context, state) {
      return TabBarView(
        controller: _tabController,
        children: [
          _buildAccountsList(state),
          _buildTagsList(state),
        ],
      );
    });
  }

  Widget _buildAccountsList(DiscoverState state) {
    if (state.isSearchLoading || _isSearching) {
      return _buildAccountsListShimmer();
    }

    if (state.searchuserList == null || state.searchuserList!.isEmpty) {
      return Center(
          child: ExceptionWidget(
        imagePath: AssetConstants.pngNoResultFound,
        title: Lang.of(context).lbl_no_result_found,
        subtitle: Lang.of(context).lbl_no_result_found_subtitle,
        showButton: false,
      ));
    }

    return ListView.builder(
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom + 80),
      shrinkWrap: true,
      physics: AlwaysScrollableScrollPhysics(),
      itemCount: state.searchuserList?.length ?? 0,
      itemBuilder: (context, index) {
        final item = state.searchuserList?[index];
        return _buildaccountListTile(
          context,
          item?.profileImage == ""
              ? AssetConstants.pngUserReomve
              : "${APIConfig.mainbaseURL}${item?.profileImage ?? ''}",
          item?.name ?? '',
          item?.userName ?? '',
          item?.userId,
          onTap: () {
            FocusScope.of(context).unfocus();
            PersistentNavBarNavigator.pushNewScreen(
              context,
              withNavBar: false,
              screen: GetUserProfileById(
                userId: item?.userId,
                stackonScreen: true,
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildTagsList(DiscoverState state) {
    if (state.isSearchLoading || _isSearching) {
      return _buildAccountsListShimmer();
    }

    if (state.hashtagData == null || state.hashtagData!.isEmpty) {
      return Center(
        child: ExceptionWidget(
          imagePath: AssetConstants.pngNoResultFound,
          title: Lang.of(context).lbl_no_result_found,
          subtitle: Lang.of(context).lbl_no_result_found_subtitle,
          showButton: false,
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: ScrollPhysics(),
      padding: EdgeInsets.only(top: 10.0.h, bottom: MediaQuery.of(context).viewInsets.bottom + 80),
      itemCount: state.hashtagData?.length,
      itemBuilder: (context, index) {
        return _buildaccountListTile(
          context,
          AssetConstants.icHastag,
          state.hashtagData?[index].name ?? '',
          '${state.hashtagData?[index].posts}',
          state.hashtagData?[index].id,
          onTap: () {
            FocusScope.of(context).unfocus();
            PersistentNavBarNavigator.pushNewScreen(
              context,
              screen: HashtagPostWidget(stackonScreen: false, hashtagData: state.hashtagData?[index]),
            );
          },
        );
      },
    );
  }

  Widget _buildaccountListTile(BuildContext context, String imagepath, String title, String subtitle, int? id,
      {VoidCallback? onTap}) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 4.0.h),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  height: 50.h,
                  width: 50.w,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(100.r),
                    border: Border.all(
                      color: (imagepath == AssetConstants.icHastag) ? Colors.white : Theme.of(context).primaryColor,
                      width: 2.w,
                    ),
                  ),
                  child: ClipRRect(
                    clipBehavior: Clip.hardEdge,
                    child: Padding(
                      padding: const EdgeInsets.all(4.0),
                      child: CustomImageView(
                        imagePath: imagepath,
                        radius: BorderRadius.circular(100.r),
                        fit: BoxFit.cover,
                        fallbackImage: AssetConstants.pngUserReomve,
                      ),
                    ),
                  ),
                ),
                buildSizedBoxW(8),
                InkWell(
                  onTap: onTap,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: Theme.of(context).textTheme.titleLarge!.copyWith(
                            fontSize: 12.sp, fontWeight: FontWeight.w700, color: Theme.of(context).primaryColor),
                      ),
                      buildSizedBoxH(2),
                      Text(
                        subtitle,
                        style: Theme.of(context).textTheme.headlineSmall!.copyWith(fontSize: 10.sp),
                      ),
                    ],
                  ),
                )
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      height: 50.h,
      margin: EdgeInsets.symmetric(horizontal: 60.w),
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: const Color(0xffF0EFEF),
        borderRadius: BorderRadius.circular(25.r),
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.black,
        dividerColor: Colors.transparent,
        indicatorSize: TabBarIndicatorSize.tab,
        overlayColor: WidgetStateColor.transparent,
        indicator: BoxDecoration(
          color: Theme.of(context).primaryColor,
          borderRadius: BorderRadius.circular(25),
        ),
        labelStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700, fontSize: 16.sp),
        unselectedLabelStyle:
            Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w700, fontSize: 16.sp),
        tabs: const [
          Tab(text: "Accounts"),
          Tab(text: "Tags"),
        ],
      ),
    );
  }

  Widget _buildAccountsListShimmer() {
    return ListView.builder(
      shrinkWrap: true,
      physics: const AlwaysScrollableScrollPhysics(),
      itemCount: 8,
      itemBuilder: (context, index) {
        return _buildAccountShimmerTile(context);
      },
    );
  }

  Widget _buildAccountShimmerTile(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4.0.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.all(4.0),
                child: Shimmer.fromColors(
                  baseColor: Colors.grey[300]!,
                  highlightColor: Colors.grey[100]!,
                  child: Container(
                    height: 48.0.h,
                    width: 50.0.w,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(40.r),
                    ),
                  ),
                ),
              ),
              buildSizedBoxW(8),
              Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Shimmer.fromColors(
                    baseColor: Colors.grey[300]!,
                    highlightColor: Colors.grey[100]!,
                    child: Container(
                      height: 16.h,
                      width: 120.w,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                    ),
                  ),
                  buildSizedBoxH(8),
                  Shimmer.fromColors(
                    baseColor: Colors.grey[300]!,
                    highlightColor: Colors.grey[100]!,
                    child: Container(
                      height: 12.h,
                      width: 80.w,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(4.r),
                      ),
                    ),
                  ),
                ],
              )
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOfflineMessage(bool showOfflineMessage) {
    return Positioned(
      bottom: Platform.isAndroid ? 80.h : 60.h,
      left: 0,
      right: 0,
      child: AnimatedSlide(
        offset: _showOfflineMessage ? Offset.zero : const Offset(0, 1),
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeInOut,
        child: AnimatedOpacity(
          opacity: _showOfflineMessage ? 1.0 : 0.0,
          duration: const Duration(milliseconds: 300),
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 10.w),
            padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.h),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withOpacity(0.95),
              borderRadius: BorderRadius.circular(10.r),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.wifi_off,
                  color: Theme.of(context).customColors.white,
                  size: 18.sp,
                ),
                buildSizedBoxW(8),
                Text(
                  "Couldn't refresh feed",
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Theme.of(context).customColors.white,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
