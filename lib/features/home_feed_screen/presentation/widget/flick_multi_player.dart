// ignore_for_file: library_private_types_in_public_api

import 'dart:io';

import 'package:flick_video_player/flick_video_player.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/flick_multi_manager.dart';
import 'package:flowkar/features/home_feed_screen/presentation/widget/portrait_controls.dart';
import 'package:video_player/video_player.dart';
import 'package:visibility_detector/visibility_detector.dart';

class FlickMultiPlayer extends StatefulWidget {
  const FlickMultiPlayer({super.key, this.url, this.file, this.image, required this.flickMultiManager});

  final String? url;
  final File? file;
  final String? image;
  final FlickMultiManager flickMultiManager;

  @override
  _FlickMultiPlayerState createState() => _FlickMultiPlayerState();
}

class _FlickMultiPlayerState extends State<FlickMultiPlayer> {
  late FlickManager flickManager;

  @override
  void initState() {
    super.initState();
    flickManager = FlickManager(
      videoPlayerController: widget.file != null
          ? VideoPlayerController.file(widget.file!)
          : VideoPlayerController.networkUrl(Uri.parse(widget.url!))
        ..setLooping(true),
      autoPlay: false,
    );
  }

  @override
  void dispose() {
    //* Removes the video player controller from the manager when the widget is disposed.
    widget.flickMultiManager.remove(flickManager);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
      key: ObjectKey(flickManager),
      onVisibilityChanged: (visiblityInfo) {
        //* Plays the video when it becomes visible (more than 60% of the video is visible).
        final isVisible = visiblityInfo.visibleFraction > 0.5;
        if (isVisible) {
          widget.flickMultiManager.play(flickManager);
        } else {
          widget.flickMultiManager.pause(); // Ensure this method correctly pauses the video.
        }
      },
      child: FlickVideoPlayer(
        flickManager: flickManager,
        flickVideoWithControls: FlickVideoWithControls(
          playerLoadingFallback: Positioned.fill(
            child: Stack(
              alignment: Alignment.center,
              children: <Widget>[
                Positioned.fill(
                  child: CustomImageView(
                    imagePath: widget.image!,
                    fit: BoxFit.cover,
                  ),
                ),
              ],
            ),
          ),
          controls: FeedPlayerPortraitControls(
            flickMultiManager: widget.flickMultiManager,
            flickManager: flickManager,
          ),
        ),
        flickVideoWithControlsFullscreen: FlickVideoWithControls(
          backgroundColor: Colors.white,
          playerLoadingFallback: Center(
              child: CustomImageView(
            imagePath: widget.image!,
            fit: BoxFit.fitWidth,
          )),
          iconThemeData: IconThemeData(size: 40.sp, color: Colors.white),
          textStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(color: Theme.of(context).customColors.white),
        ),
      ),
    );
  }
}
