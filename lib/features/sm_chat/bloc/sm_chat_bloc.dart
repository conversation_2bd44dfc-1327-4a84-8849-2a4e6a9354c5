import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flowkar/core/socket/socket_service.dart';
import 'package:flowkar/core/utils/exports.dart';
import 'package:flowkar/features/authentication/model/reset_password_response_model.dart';
import 'package:flowkar/features/sm_chat/model/ai_generate_massage_or_comment_model.dart';
import 'package:flowkar/features/sm_chat/model/deep_link_chat_model.dart';
import 'package:flowkar/features/sm_chat/model/flowkar_chat_model/chat_list_model.dart';
import 'package:flowkar/features/sm_chat/model/flowkar_chat_model/chat_message_list_model.dart';
import 'package:flowkar/features/sm_chat/model/flowkar_chat_model/search_user_model.dart';
import 'package:flowkar/features/sm_chat/model/send_insta_msg_model.dart';
import 'package:flowkar/features/sm_chat/model/single_chat_model.dart';
import 'package:flowkar/features/sm_chat/model/sm_chat_list_model.dart';
import 'package:flowkar/features/sm_chat/model/telegram_send_masage_model.dart';
import 'package:flowkar/features/sm_chat/model/telegram_user_chat_list_model.dart';
import 'package:flowkar/features/sm_chat/model/telegram_user_list_model.dart';
// import 'package:flowkar/features/sm_chat/model/telegram_send_masage_model.dart';
// import 'package:flowkar/features/sm_chat/model/telegram_user_chat_list_model.dart';
// import 'package:flowkar/features/sm_chat/model/telegram_user_list_model.dart';
import 'package:flowkar/features/sm_chat/presentation/flowkar_chat_screen.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:http/http.dart' as http;

part 'sm_chat_event.dart';
part 'sm_chat_state.dart';

const _duration = Duration(milliseconds: 300);
EventTransformer<Event> debounce<Event>(Duration duration) {
  return (events, mapper) => events.debounce(duration).switchMap(mapper);
}

class SmChatBloc extends Bloc<SmChatEvent, SmChatState> {
  ApiClient apiClient = ApiClient(Dio());

  SmChatBloc() : super(SmChatState()) {
    on<SmChatInitialEvent>(_onInitialize);
    on<GetSmchatListEvent>(_ongetSmchatListEvent);
    on<GetsinglechatdetailsEvent>(_ongetsinglechatdetailsEvent);
    on<SendInstaMessageEvent>(_onSendSendInstaMessageEvent);
    on<SendFacebookMessageEvent>(_onSendSendFacebookMessageEvent);
    on<SendMessageEvent>(_onsendMessageSocketEvent);
    on<GetChatListEvent>(_ongetChatListEvent);
    on<GetChatMessageListEvent>(_getchatMessageListApiEvent);
    on<UpdateChatMessageSocketEvent>(_onUpdateMessageListApiEvent);
    on<TypingSocketEvent>(_onTypingSocketEvent);
    on<SearchUserListEvent>(_onUserSearchEvent, transformer: debounce(_duration));
    on<ChatListMessageEvent>(_onchatListMessageEvent);
    on<DeleteChatApiEvent>(_onDeleteChatApi);
    on<RefreshChatGetApiEvent>(_onRefreshChatGetApi);
    on<FetchDeepLinkChatEvent>(_fetchDeepLinkChat);
    on<GetTelegramUerListAPI>(_onGetTelegramUserListEvent);
    on<GetTelegramUerChatListAPI>(_onGettelegramUserEvent);
    on<TelegramSendMassage>(_onSendTelegramMessageEvent);
    on<AIgenerateMassageEvent>(_aIgenerateContentApi);
    on<ClearAIGeneratedMassageEvent>(_clearAigenerateMassage);
  }

  _onInitialize(SmChatInitialEvent event, Emitter<SmChatState> emit) {
    emit(state.copyWith(
        allFetch: false,
        chatList: [],
        chatMessageList: [],
        searchuserList: [],
        hasMore: true,
        isLoadingMore: false,
        isloding: true,
        scrollController: ScrollController(),
        searchController: TextEditingController(),
        chatController: TextEditingController()));
  }

  _ongetSmchatListEvent(GetSmchatListEvent event, Emitter<SmChatState> emit) async {
    emit(state.copyWith(isloding: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.getallsmchatList(loginuserId, brandId);
      if (result.status == true) {
        emit(state.copyWith(smchatListModel: result, isloding: false));
      }
    } catch (e) {
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(isloding: false));
      });
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(isloding: false));
      });
    }
  }

  _ongetsinglechatdetailsEvent(GetsinglechatdetailsEvent event, Emitter<SmChatState> emit) async {
    emit(state.copyWith(issinglechatloding: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.getsinglechatDetails(
          loginuserId, brandId, event.platformId, event.conversationId, event.ownerId);
      if (result.status == true) {
        emit(state.copyWith(singleChatModel: result, issinglechatloding: false));
      }
    } catch (e) {
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(issinglechatloding: false));
      });
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(issinglechatloding: false));
      });
    }
  }

  _onSendSendInstaMessageEvent(SendInstaMessageEvent event, Emitter<SmChatState> emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final newmessage = ChatData(
        attachments: Attachments(),
        checkMessage: 1,
        createdTime: DateTime.now().add(Duration(hours: 6, minutes: 30)).toString(),
        from: From(),
        id: '',
        message: event.message,
        to: To(),
      );

      final updatedsendinstagreamMessageModel = state.singleChatModel?.copyWith(data: [
        ...?state.singleChatModel?.data,
        newmessage,
      ]);
      emit(state.copyWith(singleChatModel: updatedsendinstagreamMessageModel));

      if (event.file != null) {
        final result = await apiClient.sendInstagreamMessageWithoutFile(
          logInUserId: loginuserId,
          brandId: brandId,
          fromuserId: event.fromuserId,
          touserId: event.touserId,
          messageType: event.messageType,
          message: event.message,
        );

        if (result.status == true) {
          emit(state.copyWith(sendinstagreamMessageModel: result));
        }
      } else {
        final result = await apiClient.sendInstagreamMessageWithFile(
          logInUserId: loginuserId,
          brandId: brandId,
          fromuserId: event.fromuserId,
          touserId: event.touserId,
          messageType: event.messageType,
          message: event.message,
          file: event.file,
        );

        if (result.status == true) {
          emit(state.copyWith(sendinstagreamMessageModel: result));
        }
      }
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  _onSendSendFacebookMessageEvent(SendFacebookMessageEvent event, Emitter<SmChatState> emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final newmessage = ChatData(
        attachments: Attachments(),
        checkMessage: 1,
        createdTime: DateTime.now().add(Duration(hours: 6, minutes: 30)).toString(),
        from: From(),
        id: '',
        message: event.message,
        to: To(),
      );

      final updatedsendinstagreamMessageModel = state.singleChatModel?.copyWith(data: [
        ...?state.singleChatModel?.data,
        newmessage,
      ]);
      emit(state.copyWith(singleChatModel: updatedsendinstagreamMessageModel));

      if (event.file != null) {
        final result = await apiClient.sendFacebookMessageWithoutFile(
          logInUserId: loginuserId,
          brandId: brandId,
          fromuserId: event.fromuserId,
          touserId: event.touserId,
          messageType: event.messageType,
          message: event.message,
        );

        if (result.status == true) {
          emit(state.copyWith(sendinstagreamMessageModel: result));
        }
      } else {
        final result = await apiClient.sendFacebookMessageWithFile(
          logInUserId: loginuserId,
          brandId: brandId,
          fromuserId: event.fromuserId,
          touserId: event.touserId,
          messageType: event.messageType,
          message: event.message,
          file: event.file,
        );

        if (result.status == true) {
          emit(state.copyWith(sendinstagreamMessageModel: result));
        }
      }
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  _onsendMessageSocketEvent(SendMessageEvent event, Emitter<SmChatState> emit) async {
    try {
      SocketService.emit(APIConfig.sendmessage, {
        'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
        'message': event.message,
        'to': event.touserId,
        'type': event.type,
        'file': event.file
      });
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  _ongetChatListEvent(GetChatListEvent event, Emitter<SmChatState> emit) async {
    try {
      if (!event.isReload) {
        if (event.page == 1) {
          emit(state.copyWith(isloding: true, chatList: []));
        } else {
          emit(state.copyWith(isLoadingMore: true));
        }
      }
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      final result = await apiClient.getChatListApi(loginuserId, brandId, event.page);

      if (result.results?.status == true) {
        state.chatList.clear();
        final newData = result.results?.data ?? [];

        // Filter duplicate data based on unique ID
        final uniqueData = newData
            .where((newItem) => !state.chatList.any((existingItem) => existingItem.userId == newItem.userId))
            .toList();

        state.chatList.addAll(uniqueData);
        Logger.lOG("TOTAL CHAT USER LENGTH : ${state.chatList.length}");

        emit(state.copyWith(
          chatList: List.from(state.chatList), // Ensure state is updated with a new list
          page: event.page,
          allFetch: false,
          isloding: false,
          chatListModel: result,
          isLoadingMore: false,
        ));
      }
    } catch (error) {
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(isLoadingMore: false, allFetch: true, isloding: false));
      });
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(allFetch: false, isloding: false));
      });

      handleError(error);
      error is DioException
          ? error.response?.data['message'].contains("The connection errored:")
              ? Logger.lOG("Dio--- Error: $error")
              : toastification.show(
                  type: ToastificationType.error,
                  showProgressBar: false,
                  title: Text(
                    error.response?.data['message'] ?? error.message ?? '',
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                    style: GoogleFonts.montserrat(
                      fontSize: 12.0.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  autoCloseDuration: const Duration(seconds: 3),
                ) // Log Dio error
          : Logger.lOG("Dio--- Error: $error");
    }
  }

  _getchatMessageListApiEvent(GetChatMessageListEvent event, Emitter<SmChatState> emit) async {
    if (event.page == 1) {
      emit(state.copyWith(isloding: true));
    } else {
      emit(state.copyWith(isLoadingMore: true));
    }
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.getChatMessageListApi(loginuserId, event.userId, event.page, brandId.toString());
      if (result.results?.status == true) {
        state.chatMessageList.addAll(result.results?.data ?? []);
        Logger.lOG("TOTAL CHAT List LENGTH : ${state.chatMessageList.length}");

        emit(state.copyWith(
            chatMessageList: state.chatMessageList,
            page: event.page,
            allFetch: false,
            isloding: false,
            chatMessageListModel: result,
            isLoadingMore: false));
      }
    } catch (e) {
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(isLoadingMore: false, allFetch: true, isloding: false));
      });
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(allFetch: false, isloding: false));
      });
    }
  }

  _onUpdateMessageListApiEvent(UpdateChatMessageSocketEvent event, Emitter<SmChatState> emit) {
    try {
      state.chatMessageList.insert(
          0,
          ChatMessageData(
              id: event.id,
              createdAt: event.createdat,
              message: event.message,
              sentBy: event.sentby,
              type: event.type));

      emit(state.copyWith(chatMessageList: state.chatMessageList));
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  _onTypingSocketEvent(TypingSocketEvent event, Emitter<SmChatState> emit) {
    try {
      SocketService.emit(APIConfig.isTyping, {
        'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
        'to': event.userId,
        'is_typing': event.isTyping,
      });
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  // _onUserSearchEvent(SearchUserListEvent event, Emitter<SmChatState> emit) async {
  //   emit(state.copyWith(searchuserListLoading: true));
  //   try {
  //     final completer = Completer<Map<String, dynamic>>();
  //     SocketService.emit(APIConfig.searchuser, {
  //       'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
  //       'search_text': event.searchtext,
  //     });
  //     bool isCompleted = false;

  //     SocketService.response(
  //       APIConfig.searchuser,
  //       (response) {
  //         if (!isCompleted) {
  //           completer.complete(response);
  //           isCompleted = true;
  //           emit(state.copyWith(searchuserListLoading: true));
  //         }
  //       },
  //     );

  //     final response = await completer.future;
  //     Future.delayed(
  //       Duration(milliseconds: 550),
  //       () {
  //         if (response['data'] == null || response['data'].isEmpty) {
  //           emit(state.copyWith(searchuserList: [], searchuserListLoading: false));
  //         }
  //       },
  //     );

  //     final data = response['data'];
  //     List<SearchUserData> allUsers = data.map((item) {
  //       if (item is Map<String, dynamic>) {
  //         return SearchUserData(
  //           userId: item['id'],
  //           name: item['name'],
  //           userName: item['username'],
  //           profileImage: item['profile'],
  //         );
  //       }
  //       throw Exception('Invalid data format');
  //     }).toList();
  //     final searchText = event.searchtext.toLowerCase().trim();

  //     if (searchText.isEmpty) {
  //       emit(state.copyWith(searchuserListLoading: false));
  //       emit(state.copyWith(chatList: state.chatList));
  //     } else {
  //       // final filteredUsers = allUsers.where((user) {

  //       //   return user.userName?.toLowerCase().contains(searchText) ?? false;
  //       // }).toList();
  //       final filteredUsers = allUsers.where((user) {
  //         final name = user.name?.toLowerCase().trim() ?? '';
  //         final username = user.userName?.toLowerCase().trim() ?? '';
  //         return name.contains(searchText) || username.contains(searchText);
  //       }).toList();
  //       Logger.lOG(filteredUsers);
  //       emit(state.copyWith(searchuserList: filteredUsers, searchuserListLoading: false));
  //     }
  //   } catch (e) {
  //     emit(state.copyWith(searchuserListLoading: false));
  //     Logger.lOG(e.toString());
  //   }
  // }
  _onUserSearchEvent(SearchUserListEvent event, Emitter<SmChatState> emit) async {
    emit(state.copyWith(searchuserListLoading: true));

    try {
      final completer = Completer<Map<String, dynamic>>();

      // Emit search request
      SocketService.emit(APIConfig.searchuser, {
        'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
        'search_text': event.searchtext,
      });

      bool isCompleted = false;

      // Listen for socket response
      SocketService.response(
        APIConfig.searchuser,
        (response) {
          if (!isCompleted) {
            isCompleted = true;
            completer.complete(response);
          }
        },
      );

      // Wait for response
      final response = await completer.future;

      // Optional delay (awaited correctly)
      await Future.delayed(Duration(milliseconds: 550));

      final rawData = response['data'];

      if (rawData == null || rawData.isEmpty) {
        emit(state.copyWith(searchuserList: [], searchuserListLoading: false));
        return;
      }

      // Safely cast and parse response data
      final List<SearchUserData> allUsers = (rawData as List).map((item) {
        if (item is Map<String, dynamic>) {
          return SearchUserData(
            userId: item['id'],
            name: item['name'],
            userName: item['username'],
            profileImage: item['profile'],
          );
        } else {
          throw Exception('Invalid user data format');
        }
      }).toList();

      final searchText = event.searchtext.toLowerCase().trim();

      if (searchText.isEmpty) {
        emit(state.copyWith(searchuserListLoading: false));
        emit(state.copyWith(chatList: state.chatList));
        return;
      }

      // Filter users by name or username
      final filteredUsers = allUsers.where((user) {
        final name = user.name?.toLowerCase().trim() ?? '';
        final username = user.userName?.toLowerCase().trim() ?? '';
        return name.contains(searchText) || username.contains(searchText);
      }).toList();

      Logger.lOG(filteredUsers);

      emit(state.copyWith(
        searchuserList: filteredUsers,
        searchuserListLoading: false,
      ));
    } catch (e) {
      emit(state.copyWith(searchuserListLoading: false));
      Logger.lOG('Search Error: ${e.toString()}');
    }
  }

  _onchatListMessageEvent(ChatListMessageEvent event, Emitter<SmChatState> emit) async {
    final completer = Completer<Map<String, dynamic>>();
    SocketService.emit(APIConfig.chatList, {
      'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
      'page': event.page,
    });
    bool isCompleted = false;
    SocketService.response(
      APIConfig.chatList,
      (response) {
        if (!isCompleted) {
          completer.complete(response);
          isCompleted = true;
        }
      },
    );

    // Await the response
    final response = await completer.future;
    if (response['data']['results']['data'] == null) {
      emit(state.copyWith(chatList: []));
    } else {
      emit(state.copyWith(chatList: response['data']['results']['data']));
    }
  }

  _onDeleteChatApi(DeleteChatApiEvent event, Emitter<SmChatState> emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.deleteChatApi(loginuserId, event.userId.toString(), brandId.toString());
      if (result.status == true) {
        await _onRefreshChatGetApi(RefreshChatGetApiEvent(userId: event.userId.toString()), emit);
      }
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  _onRefreshChatGetApi(RefreshChatGetApiEvent event, Emitter<SmChatState> emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      final result = await apiClient.deleteChatApi(loginuserId, event.userId, brandId.toString());
      if (result.status == true) {
        NavigatorService.goBack();

        emit(state.copyWith(deletechatmodel: result));
      }
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  Future<void> _fetchDeepLinkChat(FetchDeepLinkChatEvent event, Emitter<SmChatState> emit) async {
    emit(state.copyWith(isloding: true));

    try {
      final url = Uri.parse(
        'https://api.flowkar.com/api/chat-profile-view/${event.reciverId}',
      );
      final result = await http.get(url).timeout(const Duration(seconds: 15));

      if (result.statusCode == 200) {
        final data = json.decode(result.body);

        if (data['status'] == true) {
          final args = deserializeDeepLinkChatModel(data);
          emit(state.copyWith(isloding: false, deepLinkChatModel: args));

          PersistentNavBarNavigator.pushNewScreen(
            event.context,
            withNavBar: false,
            screen: FlowkarChatScreen(isDeepLink: true, args: args.userData),
          );
        } else {
          emit(state.copyWith(isloding: false));
        }
      } else {
        emit(state.copyWith(isloding: false));
      }
    } catch (e) {
      emit(state.copyWith(isloding: false));
    }
  }

  _onGetTelegramUserListEvent(GetTelegramUerListAPI event, Emitter<SmChatState> emit) async {
    emit(state.copyWith(isloding: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.getTelegramUerList(logInUserId: loginuserId, brandId: brandId);
      if (result.status == true) {
        emit(state.copyWith(telegramUserListModel: result, isloding: false));
      }
    } catch (e) {
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(isloding: false));
      });
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(isloding: false));
      });
    }
  }

  _onGettelegramUserEvent(GetTelegramUerChatListAPI event, Emitter<SmChatState> emit) async {
    emit(state.copyWith(issinglechatloding: true));
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);
      final result = await apiClient.getTelegramUerChatList(
          logInUserId: loginuserId, brandId: brandId, targetUserId: event.targetUserId);
      if (result.status == true) {
        emit(state.copyWith(telegramUserChatListModel: result, issinglechatloding: false));
      }
    } catch (e) {
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(issinglechatloding: false));
      });
      await Future.delayed(const Duration(seconds: 1), () {
        emit(state.copyWith(issinglechatloding: false));
      });
    }
  }

  _onSendTelegramMessageEvent(TelegramSendMassage event, Emitter<SmChatState> emit) async {
    try {
      String? loginuserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID) ?? '';
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      // Create a temporary message for immediate UI update
      final tempMessage = TelegramMessage(
        id: DateTime.now().millisecondsSinceEpoch, // Temporary ID
        date: DateTime.now(),
        text: event.message,
        fromId: int.tryParse(loginuserId ?? "") ?? 0,
        toId: int.tryParse(event.chatId) ?? 0,
        isOutgoing: true,
        mediaType: null,
        mediaInfo: null,
        replyToMsgId: null,
      );

      // Add temporary message to current chat list for immediate UI update
      final currentMessages = List<TelegramMessage>.from(state.telegramUserChatListModel?.data ?? []);
      currentMessages.add(tempMessage);

      final updatedChatModel = state.telegramUserChatListModel?.copyWith(
        data: currentMessages,
      );

      emit(state.copyWith(
        telegramUserChatListModel: updatedChatModel,
      ));

      // Send the actual message to server
      final result = await apiClient.telegramSendMassage(
        logInUserId: loginuserId,
        brandId: brandId,
        targetUserId: event.chatId,
        massage: event.message,
      );

      if (result.status == true) {
        // Replace temporary message with actual message from server
        final serverMessage = TelegramMessage.fromJson(result.data.toJson());

        // Remove temporary message and add server message
        final finalMessages = List<TelegramMessage>.from(currentMessages);
        finalMessages.removeWhere((msg) => msg.id == tempMessage.id);
        finalMessages.add(serverMessage);

        // Sort messages by date (newest first for your reversed ListView)
        finalMessages.sort((a, b) => b.date.compareTo(a.date));

        final finalChatModel = state.telegramUserChatListModel?.copyWith(
          data: finalMessages,
        );

        emit(state.copyWith(
          telegramUserChatListModel: finalChatModel,
          telegramSendMassageModel: result,
        ));
      } else {
        // Handle error - remove temporary message
        final errorMessages = List<TelegramMessage>.from(currentMessages);
        errorMessages.removeWhere((msg) => msg.id == tempMessage.id);

        final errorChatModel = state.telegramUserChatListModel?.copyWith(
          data: errorMessages,
        );

        emit(state.copyWith(
          telegramUserChatListModel: errorChatModel,
        ));
      }
    } catch (e) {
      Logger.lOG(e.toString());
    }
  }

  _aIgenerateContentApi(AIgenerateMassageEvent event, Emitter<SmChatState> emit) async {
    try {
      String loginUserId = Prefobj.preferences?.get(Prefkeys.LOG_IN_USER_ID).toString() ?? "";
      int brandId = Prefobj.preferences?.get(Prefkeys.BRANDID);

      emit(state.copyWith(isAIGeneratingMassageloading: true));
      final results = await apiClient.aiGenerateMassage(
          logInUserId: loginUserId, brand: brandId.toString(), massageText: event.massageText);

      if (results.status == true) {
        emit(state.copyWith(
          isAIGeneratingMassageloading: false,
          aiGenerateMassageOrCommentModel: results,
          // chatController: TextEditingController(text: results.result),
        ));
      } else {
        emit(state.copyWith(isAIGeneratingMassageloading: false));
      }
    } catch (e) {
      Logger.lOG(e.toString());
      emit(state.copyWith(isAIGeneratingMassageloading: false));
    }
  }

  _clearAigenerateMassage(ClearAIGeneratedMassageEvent event, Emitter<SmChatState> emit) {
    emit(state.copyWith(
      aiGenerateMassageOrCommentModel: AiGenerateMassageOrComment(status: true, message: "", result: ""),
    ));
  }
}
